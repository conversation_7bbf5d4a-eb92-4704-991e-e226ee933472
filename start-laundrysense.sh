#!/bin/bash

echo "========================================"
echo "   LaundrySense Development Startup"
echo "========================================"
echo

echo "🔍 Running startup checks..."
npm run startup:check
if [ $? -ne 0 ]; then
    echo
    echo "❌ Startup checks failed. Please fix the issues before continuing."
    echo "📖 See docs/TROUBLESHOOTING.md for help."
    exit 1
fi

echo
echo "✅ Startup checks passed!"
echo

echo "🗄️  Checking if database needs seeding..."
npm run db:generate > /dev/null 2>&1

echo
echo "📊 Calculating patterns (this may take a moment)..."
npm run patterns:calculate

echo
echo "🚀 Starting LaundrySense services..."
echo

echo "📡 Starting WebSocket server on port 3001..."
# Start WebSocket server in background
npm run websocket &
WS_PID=$!

echo
echo "⏳ Waiting for WebSocket server to start..."
sleep 3

echo
echo "🌐 Starting Next.js development server on port 3000..."
# Start Next.js dev server in background
npm run dev &
DEV_PID=$!

echo
echo "✅ LaundrySense is starting up!"
echo
echo "📋 Services:"
echo "   - WebSocket Server: http://localhost:3001 (PID: $WS_PID)"
echo "   - Development Server: http://localhost:3000 (PID: $DEV_PID)"
echo "   - Dashboard: http://localhost:3000/dashboard"
echo
echo "🔧 Useful commands:"
echo "   - npm run websocket:test    (Test WebSocket connection)"
echo "   - npm run debug:patterns    (Debug pattern calculation)"
echo "   - npm run startup:check     (Run startup checks)"
echo
echo "📖 For troubleshooting, see: docs/TROUBLESHOOTING.md"
echo
echo "Press Ctrl+C to stop all services..."

# Function to cleanup on exit
cleanup() {
    echo
    echo "🛑 Stopping services..."
    kill $WS_PID 2>/dev/null
    kill $DEV_PID 2>/dev/null
    echo "✅ Services stopped."
    exit 0
}

# Set trap to cleanup on script exit
trap cleanup SIGINT SIGTERM

# Wait for user to stop
wait
