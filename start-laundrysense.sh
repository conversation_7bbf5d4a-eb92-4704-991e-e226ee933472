#!/bin/bash

echo "========================================"
echo "   LaundrySense Development Startup"
echo "========================================"
echo

echo "🔍 Running startup checks..."
npm run startup:check
if [ $? -ne 0 ]; then
    echo
    echo "❌ Startup checks failed. Please fix the issues before continuing."
    echo "📖 See docs/TROUBLESHOOTING.md for help."
    exit 1
fi

echo
echo "✅ Startup checks passed!"
echo

echo "🗄️  Checking if database needs seeding..."
npm run db:generate > /dev/null 2>&1

echo
echo "📊 Calculating patterns (this may take a moment)..."
npm run patterns:calculate

echo
echo "🚀 Starting LaundrySense integrated server..."
echo

echo "🌐 Starting Next.js + WebSocket integrated server on port 3000..."
# Start integrated server in background
npm run dev &
SERVER_PID=$!

echo
echo "✅ LaundrySense is starting up!"
echo
echo "📋 Services:"
echo "   - Integrated Server: http://localhost:3000 (PID: $SERVER_PID)"
echo "   - WebSocket: ws://localhost:3000"
echo "   - Dashboard: http://localhost:3000/dashboard"
echo
echo "🔧 Useful commands:"
echo "   - npm run websocket:test    (Test WebSocket connection)"
echo "   - npm run debug:patterns    (Debug pattern calculation)"
echo "   - npm run startup:check     (Run startup checks)"
echo
echo "📖 For troubleshooting, see: docs/TROUBLESHOOTING.md"
echo
echo "Press Ctrl+C to stop all services..."

# Function to cleanup on exit
cleanup() {
    echo
    echo "🛑 Stopping services..."
    kill $SERVER_PID 2>/dev/null
    echo "✅ Services stopped."
    exit 0
}

# Set trap to cleanup on script exit
trap cleanup SIGINT SIGTERM

# Wait for user to stop
wait
