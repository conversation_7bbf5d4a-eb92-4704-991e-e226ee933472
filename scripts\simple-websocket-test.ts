#!/usr/bin/env tsx

/**
 * Simple WebSocket Test Script
 * 
 * A simplified version to quickly test WebSocket connection
 */

import { WebSocket } from 'ws';

async function simpleWebSocketTest() {
  console.log('🔍 Simple WebSocket Connection Test\n');

  try {
    console.log('📡 Connecting to ws://localhost:3000...');
    const ws = new WebSocket('ws://localhost:3000');

    // Set timeout
    const timeout = setTimeout(() => {
      console.log('❌ Connection timeout after 5 seconds');
      ws.close();
      process.exit(1);
    }, 5000);

    ws.onopen = () => {
      clearTimeout(timeout);
      console.log('✅ WebSocket connection established successfully!');
    };

    ws.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        console.log('📦 Received message:', {
          type: message.type,
          hasPayload: !!message.payload,
          payloadKeys: message.payload ? Object.keys(message.payload) : []
        });

        if (message.type === 'INITIAL_STATE') {
          console.log('🎉 Initial state received successfully!');
          
          if (message.payload) {
            const { kpis, patterns } = message.payload;
            console.log('📊 Payload analysis:');
            console.log('   - KPIs:', kpis ? Object.keys(kpis) : 'null');
            console.log('   - Patterns:', patterns ? Object.keys(patterns) : 'null');
          }

          console.log('\n✅ Test completed successfully!');
          ws.close(1000, 'Test completed');
          process.exit(0);
        }
      } catch (error) {
        console.error('❌ Error parsing message:', error);
      }
    };

    ws.onerror = (error) => {
      clearTimeout(timeout);
      console.error('❌ WebSocket error:', error);
      process.exit(1);
    };

    ws.onclose = (event) => {
      console.log(`🔌 Connection closed: ${event.code} - ${event.reason}`);
      if (event.code !== 1000) {
        console.log('❌ Unexpected connection close');
        process.exit(1);
      }
    };

  } catch (error) {
    console.error('❌ Failed to create WebSocket connection:', error);
    process.exit(1);
  }
}

// Run the test
simpleWebSocketTest().catch((error) => {
  console.error('❌ Test failed:', error);
  process.exit(1);
});
