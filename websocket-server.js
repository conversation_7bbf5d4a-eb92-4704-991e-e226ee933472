const { WebSocketServer } = require('ws');
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();
const port = 3001;

// Create WebSocket server on port 3001
const wss = new WebSocketServer({ port });

console.log(`🚀 WebSocket server started on ws://localhost:${port}`);

// Helper function to broadcast data to all connected clients
const broadcast = (data) => {
  const jsonData = JSON.stringify(data);
  let successCount = 0;
  let errorCount = 0;

  wss.clients.forEach((client) => {
    if (client.readyState === 1) { // WebSocket.OPEN
      try {
        client.send(jsonData);
        successCount++;
      } catch (error) {
        console.error('[WSS] Error broadcasting to client:', error.message);
        errorCount++;
        // Try to close the problematic connection
        try {
          client.close(1011, 'Broadcast error');
        } catch (closeError) {
          console.error('[WSS] Error closing problematic client:', closeError.message);
        }
      }
    }
  });

  if (successCount > 0 || errorCount > 0) {
    console.log(`[WSS] Broadcast complete. Success: ${successCount}, Errors: ${errorCount}`);
  }
};

// Simplified KPI fetcher (since we can't import from Next.js easily)
const getRealTimeKpis = async () => {
  try {
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());

    // Get today's transactions
    const todayTransactions = await prisma.transaction.findMany({
      where: {
        transaction_date: {
          gte: startOfDay
        }
      }
    });

    // Calculate basic KPIs
    const totalRevenue = todayTransactions.reduce((sum, t) => sum + t.price, 0);
    const totalTransactions = todayTransactions.length;
    const avgTransactionValue = totalTransactions > 0 ? totalRevenue / totalTransactions : 0;

    // Get customer count
    const totalCustomers = await prisma.customer.count();

    return {
      totalRevenue,
      totalTransactions,
      avgTransactionValue,
      totalCustomers,
      lastUpdated: new Date().toISOString()
    };
  } catch (error) {
    console.error('Error fetching KPIs:', error);
    return null;
  }
};

// Simplified pattern data fetcher
const getPatternData = async () => {
  try {
    // Get recent customer patterns (using correct model name from schema)
    const customerPatterns = await prisma.customerPattern.findMany({
      take: 10,
      orderBy: { calculated_at: 'desc' }
    });

    // Get material usage patterns (using correct model name from schema)
    const materialPatterns = await prisma.materialUsagePattern.findMany({
      take: 10,
      orderBy: { calculated_at: 'desc' }
    });

    // Get revenue trends (using correct model name from schema)
    const revenuePatterns = await prisma.revenueTrend.findMany({
      take: 30,
      orderBy: { date: 'desc' }
    });

    return {
      customer_patterns: customerPatterns,
      material_patterns: materialPatterns,
      revenue_patterns: revenuePatterns,
      lastUpdated: new Date().toISOString()
    };
  } catch (error) {
    console.error('Error fetching patterns:', error);
    return null;
  }
};

// Central function to fetch the latest dashboard state
const fetchLatestDashboardState = async () => {
  try {
    const [kpis, patterns] = await Promise.all([
      getRealTimeKpis(),
      getPatternData(),
    ]);
    return { kpis, patterns };
  } catch (error) {
    console.error('[WSS] Error fetching latest dashboard state:', error);
    return { kpis: null, patterns: null };
  }
};

// WebSocket connection handler
wss.on('connection', async (ws) => {
  console.log('✅ WebSocket client connected');

  try {
    // Send initial state to the newly connected client
    console.log('[WSS] Fetching initial state for new client...');
    const initialState = await fetchLatestDashboardState();

    // Check if WebSocket is still open before sending
    if (ws.readyState === 1) { // WebSocket.OPEN
      ws.send(JSON.stringify({
        type: 'INITIAL_STATE',
        payload: initialState
      }));
    }
  } catch (error) {
    console.error('[WSS] Error sending initial state:', error);
  }

  // Listen for messages from this specific client
  ws.on('message', async (message) => {
    try {
      const parsedMessage = JSON.parse(message.toString());
      console.log('[WSS] Received message:', parsedMessage);

      // If a trigger is received, fetch the latest state and broadcast to ALL clients
      if (parsedMessage.type === 'TRIGGER_ANALYSIS') {
        console.log('[WSS] Analysis trigger received. Fetching and broadcasting latest state...');
        const latestState = await fetchLatestDashboardState();
        broadcast({
          type: 'DASHBOARD_STATE_UPDATE',
          payload: latestState,
        });
      }
    } catch (error) {
      console.error('[WSS] Error processing message:', error);
    }
  });

  // Handle client disconnection
  ws.on('close', (code, reason) => {
    console.log(`❌ WebSocket client disconnected. Code: ${code}, Reason: ${reason}`);
  });

  // Handle errors
  ws.on('error', (error) => {
    console.error('[WSS] WebSocket client error:', error.message);
    // Don't crash the server on client errors
    try {
      if (ws.readyState === 1) {
        ws.close(1011, 'Server error');
      }
    } catch (closeError) {
      console.error('[WSS] Error closing problematic connection:', closeError.message);
    }
  });
});

// Global error handlers
process.on('uncaughtException', (error) => {
  console.error('🚨 Uncaught Exception:', error.message);
  console.error('Stack:', error.stack);
  // Don't exit the process, just log the error
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('🚨 Unhandled Rejection at:', promise, 'reason:', reason);
  // Don't exit the process, just log the error
});

// WebSocket server error handler
wss.on('error', (error) => {
  console.error('🚨 WebSocket Server Error:', error.message);
});

// Graceful shutdown
const gracefulShutdown = async (signal) => {
  console.log(`\n🛑 Received ${signal}. Shutting down WebSocket server gracefully...`);

  try {
    // Close all client connections
    wss.clients.forEach((client) => {
      if (client.readyState === 1) {
        client.close(1001, 'Server shutting down');
      }
    });

    // Close the server
    wss.close(() => {
      console.log('✅ WebSocket server closed');
    });

    // Disconnect from database
    await prisma.$disconnect();
    console.log('✅ Database disconnected');

    process.exit(0);
  } catch (error) {
    console.error('❌ Error during shutdown:', error.message);
    process.exit(1);
  }
};

process.on('SIGINT', () => gracefulShutdown('SIGINT'));
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
