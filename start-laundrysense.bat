@echo off
echo ========================================
echo    LaundrySense Development Startup
echo ========================================
echo.

echo 🔍 Running startup checks...
call npm run startup:check
if %ERRORLEVEL% neq 0 (
    echo.
    echo ❌ Startup checks failed. Please fix the issues before continuing.
    echo 📖 See docs/TROUBLESHOOTING.md for help.
    pause
    exit /b 1
)

echo.
echo ✅ Startup checks passed!
echo.

echo 🗄️  Checking if database needs seeding...
call npm run db:generate >nul 2>&1

echo.
echo 📊 Calculating patterns (this may take a moment)...
call npm run patterns:calculate

echo.
echo 🚀 Starting LaundrySense services...
echo.
echo 📡 Starting WebSocket server on port 3001...
start "WebSocket Server" cmd /k "npm run websocket"

echo.
echo ⏳ Waiting for WebSocket server to start...
timeout /t 3 /nobreak >nul

echo.
echo 🌐 Starting Next.js development server on port 3000...
start "Next.js Dev Server" cmd /k "npm run dev"

echo.
echo ✅ LaundrySense is starting up!
echo.
echo 📋 Services:
echo    - WebSocket Server: http://localhost:3001
echo    - Development Server: http://localhost:3000
echo    - Dashboard: http://localhost:3000/dashboard
echo.
echo 🔧 Useful commands:
echo    - npm run websocket:test    (Test WebSocket connection)
echo    - npm run debug:patterns    (Debug pattern calculation)
echo    - npm run startup:check     (Run startup checks)
echo.
echo 📖 For troubleshooting, see: docs/TROUBLESHOOTING.md
echo.
echo Press any key to exit this window...
pause >nul
