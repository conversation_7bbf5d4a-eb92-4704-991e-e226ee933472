# LaundrySense Troubleshooting Guide

## 🚨 Common Issues & Solutions

### 1. WebSocket Connection Issues

#### **Problem: Loading forever on dashboard**
**Symptoms:**
- Dashboard shows loading spinner indefinitely
- Insights don't appear after refresh
- Console shows WebSocket errors

**Solutions:**

1. **Check WebSocket Server Status**
   ```bash
   # Make sure WebSocket server is running
   npm run websocket
   
   # Should show: "🚀 WebSocket server started on ws://localhost:3001"
   ```

2. **Test WebSocket Connection**
   ```bash
   npm run websocket:test
   ```

3. **Check Port Conflicts**
   - WebSocket server runs on port 3001
   - Next.js dev server runs on port 3000
   - Make sure both ports are available

4. **Verify Database Connection**
   ```bash
   npm run debug:patterns
   ```

#### **Problem: Invalid WebSocket frame error**
**Error:** `RangeError: Invalid WebSocket frame: invalid status code 30857`

**Solutions:**
1. **Restart WebSocket Server**
   ```bash
   # Stop the server (Ctrl+C)
   # Then restart
   npm run websocket
   ```

2. **Clear Browser Cache**
   - Hard refresh (Ctrl+Shift+R)
   - Clear browser cache and cookies

3. **Check Network Configuration**
   - Disable VPN if active
   - Check firewall settings
   - Ensure localhost is accessible

### 2. Pattern Calculation Issues

#### **Problem: No insights appearing**
**Symptoms:**
- Dashboard loads but shows "Sedang memuat insights..."
- Pattern data is null or empty

**Solutions:**

1. **Check Database Data**
   ```bash
   npm run debug:patterns
   ```

2. **Run Pattern Calculation Manually**
   ```bash
   npm run patterns:calculate
   ```

3. **Verify Database Seeding**
   ```bash
   npm run db:seed
   ```

4. **Check Pattern Calculation Logs**
   ```bash
   # Check for calculation errors
   npm run patterns:calculate
   ```

#### **Problem: Pattern calculation fails**
**Error:** Database connection or calculation errors

**Solutions:**
1. **Check Database Connection**
   ```bash
   # Verify DATABASE_URL in .env
   npm run db:generate
   npm run db:push
   ```

2. **Reset Database (if needed)**
   ```bash
   npm run db:reset
   npm run db:seed
   ```

### 3. Type Safety Issues

#### **Problem: TypeScript errors in DashboardContext**
**Symptoms:**
- Build fails with type errors
- IDE shows red underlines

**Solutions:**
1. **Regenerate Prisma Client**
   ```bash
   npm run db:generate
   ```

2. **Check Type Imports**
   - Verify all imports are correct
   - Check for circular dependencies

3. **Clear TypeScript Cache**
   ```bash
   # Delete .next folder
   rm -rf .next
   npm run build
   ```

### 4. Development Server Issues

#### **Problem: Port conflicts**
**Error:** `EADDRINUSE: address already in use :::3000`

**Solutions:**
1. **Kill Existing Processes**
   ```bash
   # Windows
   netstat -ano | findstr :3000
   taskkill /PID <PID> /F
   
   # macOS/Linux
   lsof -ti:3000 | xargs kill -9
   ```

2. **Use Different Ports**
   ```bash
   # Start on different port
   PORT=3002 npm run dev
   ```

### 5. Database Issues

#### **Problem: Database connection fails**
**Error:** `Can't reach database server`

**Solutions:**
1. **Check MySQL Service**
   ```bash
   # Windows
   net start mysql
   
   # macOS
   brew services start mysql
   
   # Linux
   sudo systemctl start mysql
   ```

2. **Verify Database Credentials**
   ```env
   # Check .env file
   DATABASE_URL="mysql://username:password@localhost:3306/laundrysense"
   ```

3. **Create Database**
   ```sql
   CREATE DATABASE laundrysense;
   ```

## 🛠️ Debugging Tools

### Available Scripts
```bash
# Test WebSocket connection
npm run websocket:test

# Debug pattern calculation
npm run debug:patterns

# Test context detection
npm run context:test

# Test insight generation
npm run insights:test

# Calculate patterns manually
npm run patterns:calculate
```

### Useful Commands
```bash
# Check database status
npm run db:generate && npm run db:push

# Reset everything
npm run db:reset && npm run db:seed

# Full restart
npm run websocket & npm run dev
```

## 📊 Monitoring & Logs

### WebSocket Server Logs
- Connection status messages
- Error messages with stack traces
- Client connection/disconnection events

### Pattern Calculation Logs
- Calculation start/completion times
- Records processed/updated counts
- Error details and stack traces

### Browser Console Logs
- WebSocket connection status
- Data reception confirmations
- Error messages and warnings

## 🔍 Step-by-Step Debugging

### When Dashboard Won't Load
1. Check if WebSocket server is running
2. Test WebSocket connection with script
3. Verify database has data
4. Run pattern calculation manually
5. Check browser console for errors
6. Clear cache and hard refresh

### When Insights Don't Appear
1. Run debug script for patterns
2. Check if pattern data exists in database
3. Verify context detection is working
4. Test insight generation manually
5. Check for type errors in console

### When WebSocket Keeps Disconnecting
1. Check server logs for errors
2. Verify port availability
3. Test with different browser
4. Check network configuration
5. Restart both servers

## 📞 Getting Help

If issues persist:
1. Check the logs carefully
2. Run all debug scripts
3. Verify environment setup
4. Check for recent code changes
5. Consider database reset as last resort

Remember to backup your data before making major changes!
