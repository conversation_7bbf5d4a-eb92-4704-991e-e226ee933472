#!/usr/bin/env tsx

/**
 * WebSocket Connection Test Script
 *
 * This script tests the WebSocket connection and verifies that:
 * 1. WebSocket server is running and accessible
 * 2. Initial state is received correctly
 * 3. Pattern data is available
 * 4. KPI data is structured correctly
 */

import { WebSocket } from 'ws';

interface TestResult {
  success: boolean;
  message: string;
  data?: any;
  error?: string;
}

class WebSocketTester {
  private ws: WebSocket | null = null;
  private results: TestResult[] = [];

  async runTests(): Promise<void> {
    console.log('🧪 Starting WebSocket Connection Tests...\n');

    try {
      await this.testConnection();
      await this.testInitialStateAndDataStructure(); // Combined test
    } catch (error) {
      this.addResult(false, 'Test suite failed', undefined, error instanceof Error ? error.message : String(error));
    } finally {
      this.cleanup();
      this.printResults();
    }
  }

  private async testConnection(): Promise<void> {
    return new Promise((resolve, reject) => {
      console.log('📡 Testing WebSocket connection...');

      try {
        this.ws = new WebSocket('ws://localhost:3000');

        const timeout = setTimeout(() => {
          this.addResult(false, 'Connection test', undefined, 'Connection timeout after 5 seconds');
          reject(new Error('Connection timeout'));
        }, 5000);

        this.ws.onopen = () => {
          clearTimeout(timeout);
          this.addResult(true, 'WebSocket connection established');
          console.log('✅ Connection successful\n');
          resolve();
        };

        this.ws.onerror = (error) => {
          clearTimeout(timeout);
          this.addResult(false, 'WebSocket connection failed', undefined, error.toString());
          reject(error);
        };

        this.ws.onclose = (event) => {
          console.log(`🔌 Connection closed: ${event.code} - ${event.reason}`);
        };

      } catch (error) {
        this.addResult(false, 'WebSocket connection failed', undefined, error instanceof Error ? error.message : String(error));
        reject(error);
      }
    });
  }

  private async testInitialStateAndDataStructure(): Promise<void> {
    return new Promise((resolve, reject) => {
      console.log('📊 Testing initial state reception and data structure...');

      if (!this.ws) {
        this.addResult(false, 'Initial state test', undefined, 'No WebSocket connection');
        reject(new Error('No WebSocket connection'));
        return;
      }

      const timeout = setTimeout(() => {
        this.addResult(false, 'Initial state test', undefined, 'No initial state received within 10 seconds');
        reject(new Error('Initial state timeout'));
      }, 10000);

      // Single message handler for all tests
      this.ws.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          console.log('📦 Received message type:', message.type);

          if (message.type === 'INITIAL_STATE') {
            clearTimeout(timeout);

            // Test 1: Initial state reception
            this.addResult(true, 'Initial state received', {
              messageType: message.type,
              hasPayload: !!message.payload,
              hasKpis: !!message.payload?.kpis,
              hasPatterns: !!message.payload?.patterns
            });
            console.log('✅ Initial state received');

            // Test 2: Data structure validation
            if (message.payload) {
              const { kpis, patterns } = message.payload;
              console.log('🔍 Testing data structure...');

              // Test KPI structure
              if (kpis) {
                console.log('📊 KPI data keys:', Object.keys(kpis));
                const kpiTests = {
                  hasTotalRevenue: typeof kpis.totalRevenue !== 'undefined',
                  hasTotalTransactions: typeof kpis.totalTransactions !== 'undefined',
                  hasTotalCustomers: typeof kpis.totalCustomers !== 'undefined',
                  hasLastUpdated: typeof kpis.lastUpdated !== 'undefined'
                };

                this.addResult(
                  Object.values(kpiTests).some(Boolean), // At least one field should exist
                  'KPI data structure validation',
                  { kpiTests, actualKpis: Object.keys(kpis) }
                );
              } else {
                this.addResult(false, 'KPI data structure validation', undefined, 'No KPI data received');
              }

              // Test Pattern structure
              if (patterns) {
                console.log('📈 Pattern data keys:', Object.keys(patterns));
                const patternTests = {
                  hasCustomerPatterns: Array.isArray(patterns.customer_patterns),
                  hasMaterialPatterns: Array.isArray(patterns.material_patterns),
                  hasRevenuePatterns: Array.isArray(patterns.revenue_patterns),
                  hasLastUpdated: typeof patterns.lastUpdated !== 'undefined'
                };

                this.addResult(
                  Object.values(patternTests).some(Boolean), // At least one field should exist
                  'Pattern data structure validation',
                  { patternTests, actualPatterns: Object.keys(patterns) }
                );
              } else {
                this.addResult(false, 'Pattern data structure validation', undefined, 'No pattern data received');
              }

              console.log('✅ Data structure validation complete\n');
            } else {
              this.addResult(false, 'Data structure validation', undefined, 'No payload in initial state');
            }

            resolve();
          }
        } catch (error) {
          clearTimeout(timeout);
          this.addResult(false, 'Message parsing failed', undefined, error instanceof Error ? error.message : String(error));
          reject(error);
        }
      };
    });
  }

  private addResult(success: boolean, message: string, data?: any, error?: string): void {
    this.results.push({ success, message, data, error });
  }

  private cleanup(): void {
    if (this.ws) {
      this.ws.close(1000, 'Test completed');
      this.ws = null;
    }
  }

  private printResults(): void {
    console.log('📋 Test Results Summary:');
    console.log('========================\n');

    let passedTests = 0;
    let totalTests = this.results.length;

    this.results.forEach((result, index) => {
      const status = result.success ? '✅ PASS' : '❌ FAIL';
      console.log(`${index + 1}. ${status}: ${result.message}`);

      if (result.data) {
        console.log(`   Data: ${JSON.stringify(result.data, null, 2)}`);
      }

      if (result.error) {
        console.log(`   Error: ${result.error}`);
      }

      console.log('');

      if (result.success) passedTests++;
    });

    console.log(`📊 Summary: ${passedTests}/${totalTests} tests passed`);

    if (passedTests === totalTests) {
      console.log('🎉 All tests passed! WebSocket connection is working correctly.');
    } else {
      console.log('⚠️  Some tests failed. Please check the WebSocket server and database.');
    }
  }
}

// Run the tests
async function main() {
  const tester = new WebSocketTester();
  await tester.runTests();
  process.exit(0);
}

if (require.main === module) {
  main().catch((error) => {
    console.error('❌ Test runner failed:', error);
    process.exit(1);
  });
}
