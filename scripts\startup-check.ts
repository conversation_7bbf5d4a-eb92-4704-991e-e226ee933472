#!/usr/bin/env tsx

/**
 * LaundrySense Startup Check Script
 * 
 * This script performs comprehensive startup checks to ensure
 * all systems are ready before starting the application.
 */

import { PrismaClient } from '@prisma/client';
import { WebSocket } from 'ws';
import { existsSync } from 'fs';
import { join } from 'path';

const prisma = new PrismaClient();

interface CheckResult {
  name: string;
  status: 'pass' | 'fail' | 'warning';
  message: string;
  details?: any;
}

class StartupChecker {
  private results: CheckResult[] = [];

  async runAllChecks(): Promise<boolean> {
    console.log('🚀 LaundrySense Startup Check\n');
    console.log('============================\n');

    try {
      await this.checkEnvironment();
      await this.checkDatabase();
      await this.checkDataAvailability();
      await this.checkWebSocketServer();
      await this.checkPatternCalculation();
      
      return this.printResults();
    } catch (error) {
      console.error('❌ Startup check failed:', error);
      return false;
    } finally {
      await prisma.$disconnect();
    }
  }

  private async checkEnvironment(): Promise<void> {
    console.log('🔧 Checking environment...');

    // Check .env file
    const envPath = join(process.cwd(), '.env');
    if (existsSync(envPath)) {
      this.addResult('Environment File', 'pass', '.env file exists');
    } else {
      this.addResult('Environment File', 'fail', '.env file not found');
    }

    // Check DATABASE_URL
    if (process.env.DATABASE_URL) {
      this.addResult('Database URL', 'pass', 'DATABASE_URL is configured');
    } else {
      this.addResult('Database URL', 'fail', 'DATABASE_URL not found in environment');
    }

    // Check Node.js version
    const nodeVersion = process.version;
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
    if (majorVersion >= 18) {
      this.addResult('Node.js Version', 'pass', `Node.js ${nodeVersion} (compatible)`);
    } else {
      this.addResult('Node.js Version', 'warning', `Node.js ${nodeVersion} (recommend 18+)`);
    }

    console.log('✅ Environment check completed\n');
  }

  private async checkDatabase(): Promise<void> {
    console.log('🗄️  Checking database...');

    try {
      // Test connection
      await prisma.$connect();
      this.addResult('Database Connection', 'pass', 'Successfully connected to database');

      // Check if tables exist
      const tables = await prisma.$queryRaw`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = DATABASE()
      ` as any[];

      const requiredTables = [
        'customers', 'materials', 'transactions', 'transaction_materials',
        'customer_patterns', 'material_usage_patterns', 'revenue_trends'
      ];

      const existingTables = tables.map((t: any) => t.table_name || t.TABLE_NAME);
      const missingTables = requiredTables.filter(table => !existingTables.includes(table));

      if (missingTables.length === 0) {
        this.addResult('Database Schema', 'pass', 'All required tables exist');
      } else {
        this.addResult('Database Schema', 'fail', `Missing tables: ${missingTables.join(', ')}`);
      }

    } catch (error) {
      this.addResult('Database Connection', 'fail', `Database connection failed: ${error instanceof Error ? error.message : String(error)}`);
    }

    console.log('✅ Database check completed\n');
  }

  private async checkDataAvailability(): Promise<void> {
    console.log('📊 Checking data availability...');

    try {
      const [customerCount, materialCount, transactionCount] = await Promise.all([
        prisma.customer.count(),
        prisma.material.count(),
        prisma.transaction.count()
      ]);

      // Check customers
      if (customerCount > 0) {
        this.addResult('Customer Data', 'pass', `${customerCount} customers found`);
      } else {
        this.addResult('Customer Data', 'warning', 'No customers found - consider running db:seed');
      }

      // Check materials
      if (materialCount > 0) {
        this.addResult('Material Data', 'pass', `${materialCount} materials found`);
      } else {
        this.addResult('Material Data', 'warning', 'No materials found - consider running db:seed');
      }

      // Check transactions
      if (transactionCount > 0) {
        this.addResult('Transaction Data', 'pass', `${transactionCount} transactions found`);
      } else {
        this.addResult('Transaction Data', 'warning', 'No transactions found - consider running db:seed');
      }

      // Check pattern data
      const [customerPatterns, materialPatterns, revenuePatterns] = await Promise.all([
        prisma.customerPattern.count(),
        prisma.materialUsagePattern.count(),
        prisma.revenueTrend.count()
      ]);

      if (customerPatterns > 0 || materialPatterns > 0 || revenuePatterns > 0) {
        this.addResult('Pattern Data', 'pass', `Patterns exist (C:${customerPatterns}, M:${materialPatterns}, R:${revenuePatterns})`);
      } else {
        this.addResult('Pattern Data', 'warning', 'No pattern data found - consider running patterns:calculate');
      }

    } catch (error) {
      this.addResult('Data Availability', 'fail', `Data check failed: ${error instanceof Error ? error.message : String(error)}`);
    }

    console.log('✅ Data availability check completed\n');
  }

  private async checkWebSocketServer(): Promise<void> {
    console.log('🔌 Checking WebSocket server...');

    return new Promise((resolve) => {
      const timeout = setTimeout(() => {
        this.addResult('WebSocket Server', 'fail', 'WebSocket server not responding (timeout after 5s)');
        resolve();
      }, 5000);

      try {
        const ws = new WebSocket('ws://localhost:3001');

        ws.onopen = () => {
          clearTimeout(timeout);
          this.addResult('WebSocket Server', 'pass', 'WebSocket server is running and accessible');
          ws.close(1000, 'Startup check completed');
          resolve();
        };

        ws.onerror = (error) => {
          clearTimeout(timeout);
          this.addResult('WebSocket Server', 'fail', 'WebSocket server connection failed - make sure to run "npm run websocket"');
          resolve();
        };

        ws.onmessage = (event) => {
          try {
            const message = JSON.parse(event.data);
            if (message.type === 'INITIAL_STATE') {
              this.addResult('WebSocket Data', 'pass', 'WebSocket server is sending data correctly');
            }
          } catch (error) {
            this.addResult('WebSocket Data', 'warning', 'WebSocket data format issue');
          }
        };

      } catch (error) {
        clearTimeout(timeout);
        this.addResult('WebSocket Server', 'fail', `WebSocket connection error: ${error instanceof Error ? error.message : String(error)}`);
        resolve();
      }
    });
  }

  private async checkPatternCalculation(): Promise<void> {
    console.log('⚙️  Checking pattern calculation...');

    try {
      // Import and test pattern calculation
      const { PatternCalculationService } = await import('../src/lib/pattern-analysis/pattern-calculator');
      
      // Just check if the service can be instantiated
      const service = new PatternCalculationService();
      this.addResult('Pattern Calculator', 'pass', 'Pattern calculation service is available');

      // Check if we have recent calculations
      const recentLog = await prisma.patternCalculationLog.findFirst({
        orderBy: { started_at: 'desc' },
        where: { status: 'completed' }
      });

      if (recentLog) {
        const timeSinceLastCalc = Date.now() - recentLog.started_at.getTime();
        const hoursSince = Math.floor(timeSinceLastCalc / (1000 * 60 * 60));
        
        if (hoursSince < 24) {
          this.addResult('Recent Calculations', 'pass', `Last calculation: ${hoursSince} hours ago`);
        } else {
          this.addResult('Recent Calculations', 'warning', `Last calculation: ${hoursSince} hours ago - consider running patterns:calculate`);
        }
      } else {
        this.addResult('Recent Calculations', 'warning', 'No recent pattern calculations found');
      }

    } catch (error) {
      this.addResult('Pattern Calculator', 'fail', `Pattern calculation check failed: ${error instanceof Error ? error.message : String(error)}`);
    }

    console.log('✅ Pattern calculation check completed\n');
  }

  private addResult(name: string, status: 'pass' | 'fail' | 'warning', message: string, details?: any): void {
    this.results.push({ name, status, message, details });
  }

  private printResults(): boolean {
    console.log('📋 Startup Check Results:');
    console.log('=========================\n');

    let passCount = 0;
    let warningCount = 0;
    let failCount = 0;

    this.results.forEach((result, index) => {
      let icon = '';
      switch (result.status) {
        case 'pass':
          icon = '✅';
          passCount++;
          break;
        case 'warning':
          icon = '⚠️ ';
          warningCount++;
          break;
        case 'fail':
          icon = '❌';
          failCount++;
          break;
      }

      console.log(`${index + 1}. ${icon} ${result.name}: ${result.message}`);
      if (result.details) {
        console.log(`   Details: ${JSON.stringify(result.details)}`);
      }
    });

    console.log(`\n📊 Summary: ${passCount} passed, ${warningCount} warnings, ${failCount} failed\n`);

    if (failCount === 0) {
      console.log('🎉 All critical checks passed! You can start the application.');
      if (warningCount > 0) {
        console.log('💡 Consider addressing the warnings for optimal performance.');
      }
      return true;
    } else {
      console.log('🚨 Some critical checks failed. Please fix the issues before starting.');
      console.log('\n📖 For help, see: docs/TROUBLESHOOTING.md');
      return false;
    }
  }
}

// Run the startup check
async function main() {
  const checker = new StartupChecker();
  const success = await checker.runAllChecks();
  process.exit(success ? 0 : 1);
}

if (require.main === module) {
  main().catch((error) => {
    console.error('❌ Startup check failed:', error);
    process.exit(1);
  });
}
