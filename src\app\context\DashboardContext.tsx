"use client";

import React, { createContext, useContext, useState, useEffect, ReactNode, ElementType } from 'react';
import { Zap, Eye, Calendar, TrendingUp, DollarSign, ShoppingCart, Users } from 'lucide-react';

// --- TYPE DEFINITIONS ---
import {
  GeneratedInsight,
  InsightGenerationInput,
  RealTimeData,
  OverduePickupItem
} from '../../lib/contextual-intelligence/types/insight-types';
import {
  type PatternData,
  type CurrentContextObject,
  type ContextMode as AppContextMode
} from '../../lib/contextual-intelligence/types';
import { SmartInsightGenerator } from '../../lib/contextual-intelligence/engines/smart-insight-generator';
import { ContextDetector } from '../../lib/contextual-intelligence/context-detector';
import { useDashboardWebSocket } from '../../hooks/useWebSocket';

export type ContextMode = 'morning' | 'midday' | 'evening' | 'planning';

export interface ContextConfig {
  title: string;
  subtitle: string;
  color: string;
  Icon: ElementType;
}

export interface Kpi {
  id: string;
  label: string;
  value: string;
  icon: React.ElementType;
  trend: string;
  color: string;
}

export interface LowStockMaterial {
  id: string;
  name: string;
  currentStock: number;
  minStock: number;
  unit: string;
}

export interface OverduePickupAPIResponseItem {
  transaction_id: string;
  customer_name: string;
  customer_phone: string;
  service_name: string;
  total_weight: number;
  pickup_date: string;
  days_overdue: number;
}

interface DashboardContextType {
  currentTime: Date;
  contextMode: ContextMode;
  setContextMode: React.Dispatch<React.SetStateAction<ContextMode>>;
  currentConfig: ContextConfig;
  insights: GeneratedInsight[];
  contextConfig: Record<ContextMode, ContextConfig>;
  kpis: Kpi[];
  lowStockAlerts: LowStockMaterial[];
  overduePickupAlerts: OverduePickupAPIResponseItem[];
  realTimeKpis: Partial<RealTimeData>;
  patternData: PatternData | null;
}

const contextConfig: Record<ContextMode, ContextConfig> = {
  morning: {
    title: "Siap Operasional",
    subtitle: "3 hal yang perlu dihandle hari ini",
    color: "from-amber-500 to-orange-500",
    Icon: Zap
  },
  midday: {
    title: "Monitoring Mode",
    subtitle: "Semua berjalan normal",
    color: "from-blue-500 to-cyan-500",
    Icon: Eye
  },
  evening: {
    title: "Review & Planning",
    subtitle: "Hari ini & persiapan besok",
    color: "from-purple-500 to-pink-500",
    Icon: Calendar
  },
  planning: {
    title: "Strategic Mode",
    subtitle: "Waktu untuk planning jangka panjang",
    color: "from-green-500 to-teal-500",
    Icon: TrendingUp
  }
};

const mockKpis: Kpi[] = [
  {
    id: 'revenue',
    label: 'Pendapatan Hari Ini',
    value: 'Rp 0',
    icon: DollarSign,
    trend: 'Memuat...',
    color: 'text-gray-500',
  },
  {
    id: 'orders',
    label: 'Pesanan Aktif',
    value: '0',
    icon: ShoppingCart,
    trend: 'Memuat...',
    color: 'text-gray-500',
  },
  {
    id: 'customers',
    label: 'Pelanggan Baru',
    value: '0',
    icon: Users,
    trend: 'Memuat...',
    color: 'text-gray-500',
  },
  {
    id: 'alerts',
    label: 'Mesin Siaga',
    value: '0 / 0',
    icon: Zap,
    trend: 'Memuat...',
    color: 'text-gray-500',
  },
];

const DashboardContext = createContext<DashboardContextType | undefined>(undefined);

export const DashboardProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [contextMode, setContextMode] = useState<ContextMode>('morning');
  const [kpis, setKpis] = useState<Kpi[]>(mockKpis);
  const [lowStockAlerts, setLowStockAlerts] = useState<LowStockMaterial[]>([]);
  const [overduePickupAlerts, setOverduePickupAlerts] = useState<OverduePickupAPIResponseItem[]>([]);
  const [insights, setInsights] = useState<GeneratedInsight[]>([]);
  const [realTimeKpis, setRealTimeKpis] = useState<Partial<RealTimeData>>({});
  const [patternData, setPatternData] = useState<PatternData | null>(null);

  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 60000);
    return () => clearInterval(timer);
  }, []);

  useEffect(() => {
    const hour = currentTime.getHours();
    if (hour >= 7 && hour < 10) setContextMode('morning');
    else if (hour >= 10 && hour < 16) setContextMode('midday');
    else if (hour >= 17 && hour < 20) setContextMode('evening');
    else setContextMode('planning');
  }, [currentTime]);

  useEffect(() => {
    const fetchInventoryStatus = async () => {
      try {
        const response = await fetch('/api/inventory/low-stock');
        if (!response.ok) throw new Error(`API call failed: ${response.status}`);
        const data: LowStockMaterial[] = await response.json();
        setLowStockAlerts(data);
      } catch (error) {
        console.error("Failed to fetch inventory status:", error);
        setLowStockAlerts([]);
      }
    };
    fetchInventoryStatus();
  }, []);

  useEffect(() => {
    const fetchOverduePickups = async () => {
      try {
        const response = await fetch('/api/transactions/overdue-pickups');
        if (!response.ok) throw new Error(`API call failed: ${response.status}`);
        const data: OverduePickupAPIResponseItem[] = await response.json();
        setOverduePickupAlerts(data);
      } catch (error) {
        console.error("Failed to fetch overdue pickups:", error);
        setOverduePickupAlerts([]);
      }
    };
    fetchOverduePickups();
  }, []);

  // --- REAL-TIME DATA WITH WEBSOCKET ---
  useEffect(() => {
    let ws: WebSocket | null = null;
    let reconnectTimeout: NodeJS.Timeout | null = null;
    let reconnectAttempts = 0;
    const maxReconnectAttempts = 5;
    const reconnectInterval = 3000;

    const connectWebSocket = () => {
      try {
        // Use integrated server (same port as Next.js)
        const wsUrl = `ws://${window.location.hostname}:3000`;
        console.log('[WebSocket] Attempting to connect to:', wsUrl);

        ws = new WebSocket(wsUrl);

        ws.onopen = () => {
          console.log('[WebSocket] Connection established successfully');
          reconnectAttempts = 0; // Reset reconnect attempts on successful connection
        };

        ws.onmessage = (event) => {
          try {
            const message = JSON.parse(event.data);

            // We handle two types of messages: the initial full state, and subsequent updates
            if (message.type === 'INITIAL_STATE' || message.type === 'DASHBOARD_STATE_UPDATE') {
              console.log(`[WebSocket] Received ${message.type}`, message.payload);
              const { kpis, patterns } = message.payload;

              if (kpis) {
                setRealTimeKpis(kpis);
              }
              if (patterns) {
                setPatternData(patterns);
              }
            }
          } catch (error) {
            console.error('[WebSocket] Error processing message:', error);
          }
        };

        ws.onerror = (error) => {
          console.error('[WebSocket] Connection error:', error);
        };

        ws.onclose = (event) => {
          console.log('[WebSocket] Connection closed. Code:', event.code, 'Reason:', event.reason);

          // Attempt to reconnect if not a normal closure and we haven't exceeded max attempts
          if (event.code !== 1000 && reconnectAttempts < maxReconnectAttempts) {
            reconnectAttempts++;
            console.log(`[WebSocket] Attempting to reconnect (${reconnectAttempts}/${maxReconnectAttempts}) in ${reconnectInterval}ms...`);

            reconnectTimeout = setTimeout(() => {
              connectWebSocket();
            }, reconnectInterval);
          } else if (reconnectAttempts >= maxReconnectAttempts) {
            console.error('[WebSocket] Max reconnection attempts reached. Please refresh the page.');
          }
        };

      } catch (error) {
        console.error('[WebSocket] Failed to create connection:', error);
      }
    };

    // Initial connection
    connectWebSocket();

    // Cleanup function
    return () => {
      if (reconnectTimeout) {
        clearTimeout(reconnectTimeout);
      }

      if (ws) {
        // Use normal close code to avoid invalid frame error
        ws.close(1000, 'Component unmounting');
        ws = null;
      }
    };
  }, []); // Empty dependency array ensures this effect runs only once on mount

  useEffect(() => {
    // Only update KPIs if we have meaningful real-time data
    if (realTimeKpis && Object.keys(realTimeKpis).length > 0) {
      try {
        // Handle the data structure from WebSocket server (getRealTimeKpis function)
        const kpiData = realTimeKpis as any; // Type assertion for WebSocket data

        const updatedKpis: Kpi[] = [
          {
            id: 'revenue',
            label: 'Pendapatan Hari Ini',
            value: `Rp ${(kpiData.totalRevenue || 0).toLocaleString('id-ID')}`,
            icon: DollarSign,
            trend: kpiData.totalRevenue ? `${kpiData.totalTransactions || 0} transaksi` : 'Memuat...',
            color: kpiData.totalRevenue && kpiData.totalRevenue > 0 ? 'text-green-500' : 'text-gray-500',
          },
          {
            id: 'orders',
            label: 'Transaksi Hari Ini',
            value: `${kpiData.totalTransactions || '0'}`,
            icon: ShoppingCart,
            trend: kpiData.avgTransactionValue ? `Avg: Rp ${kpiData.avgTransactionValue.toLocaleString('id-ID')}` : 'Memuat...',
            color: 'text-sky-500',
          },
          {
            id: 'customers',
            label: 'Total Pelanggan',
            value: `${kpiData.totalCustomers || '0'}`,
            icon: Users,
            trend: 'Terdaftar',
            color: 'text-indigo-500',
          },
          {
            id: 'alerts',
            label: 'Status Sistem',
            value: kpiData.lastUpdated ? 'Online' : 'Offline',
            icon: Zap,
            trend: kpiData.lastUpdated ? `Update: ${new Date(kpiData.lastUpdated).toLocaleTimeString('id-ID')}` : 'Tidak tersambung',
            color: kpiData.lastUpdated ? 'text-green-500' : 'text-red-500',
          },
        ];
        setKpis(updatedKpis);
      } catch (error) {
        console.error('Error updating KPIs:', error);
        // Keep the existing KPIs if there's an error
      }
    }
  }, [realTimeKpis]);

  useEffect(() => {
    if (!patternData) {
      setInsights([]);
      return;
    }

    const generator = new SmartInsightGenerator();
    const contextDetector = new ContextDetector();

    // Use the real ContextDetector to get current context
    const contextDetectionResult = contextDetector.detectContext({
      currentTimestamp: currentTime,
      recentUserActivityLog: [], // Could be populated with real user activity data
      historicalBusinessData: {
        monthly_transactions: [],
        material_usage_averages: [],
        customer_metrics: {
          total_customers: 0,
          active_customers_last_30_days: 0,
          average_transaction_value: 0,
          customer_retention_rate: 0.8,
        },
        revenue_trends: {
          daily_average: 0,
          weekly_average: 0,
          monthly_average: 0,
          growth_rate_percentage: 0,
        },
      },
      dataCompletenessMetrics: {
        transactions: {
          last_updated: new Date().toISOString(),
          completeness_percentage: 0.95,
          total_records: 0,
          missing_fields_count: 0,
        },
        customers: {
          last_updated: new Date().toISOString(),
          completeness_percentage: 0.90,
          total_records: 0,
          missing_fields_count: 0,
        },
        materials: {
          last_updated: new Date().toISOString(),
          completeness_percentage: 0.85,
          total_records: 0,
          missing_fields_count: 0,
        },
        patterns: {
          last_calculated: new Date().toISOString(),
          customer_patterns_count: 0,
          material_patterns_count: 0,
          revenue_trends_count: 0,
          average_confidence_score: 0.7,
        },
      },
      patternData: patternData,
    });

    const currentContextObject = contextDetectionResult.currentContextObject;

    const mappedOverduePickupItems: OverduePickupItem[] = overduePickupAlerts.map(item => ({
      transactionId: item.transaction_id,
      customerName: item.customer_name,
      customerPhone: item.customer_phone,
      serviceType: item.service_name,
      weightKg: item.total_weight,
      pickupDate: item.pickup_date,
      daysOverdue: item.days_overdue,
    }));

    // Ensure we have a valid RealTimeData object, even if some parts are missing
    const comprehensiveRealTimeData: RealTimeData = {
      transactions: realTimeKpis.transactions || {
        today_count: 0,
        today_revenue: 0,
        current_hour_count: 0,
        average_transaction_value: 0,
        peak_hour_today: null,
      },
      materials: realTimeKpis.materials || lowStockAlerts.map(item => ({
        material_id: item.id,
        material_name: item.name,
        current_stock: item.currentStock,
        minimum_threshold: item.minStock,
        category: 'default',
        last_restock_date: new Date().toISOString(),
        usage_rate_per_day: 0, // Placeholder
        days_until_empty: 99, // Placeholder
      })),
      customers: realTimeKpis.customers || {
        new_customers_today: 0,
        returning_customers_today: 0,
        pending_orders: 0,
      },
      overduePickupItems: mappedOverduePickupItems,
      operations: realTimeKpis.operations || {
        active_machines: 0,
        total_machines: 5,
        queue_length: 0,
        estimated_completion_times: [],
        staff_on_duty: 0,
      },
    };

    const insightInput: InsightGenerationInput = {
      contextObject: currentContextObject,
      patternData: patternData,
      realTimeData: comprehensiveRealTimeData,
      timestamp: currentTime,
    };

    try {
      const result = generator.generateInsights(insightInput);
      setInsights(result.insights);
    } catch (error) {
      console.error("Error generating insights:", error);
      setInsights([]);
    }
  }, [currentTime, contextMode, lowStockAlerts, overduePickupAlerts, realTimeKpis, patternData]);

  const value = {
    currentTime,
    contextMode,
    setContextMode,
    currentConfig: contextConfig[contextMode],
    insights,
    contextConfig,
    kpis,
    lowStockAlerts,
    overduePickupAlerts,
    realTimeKpis,
    patternData,
  };

  return (
    <DashboardContext.Provider value={value}>
      {children}
    </DashboardContext.Provider>
  );
};

export const useDashboard = (): DashboardContextType => {
  const context = useContext(DashboardContext);
  if (context === undefined) {
    throw new Error('useDashboard must be used within a DashboardProvider');
  }
  return context;
};
