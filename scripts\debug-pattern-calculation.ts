#!/usr/bin/env tsx

/**
 * Pattern Calculation Debug Script
 * 
 * This script helps debug pattern calculation issues by:
 * 1. Checking database connectivity
 * 2. Verifying data availability
 * 3. Testing pattern calculation functions
 * 4. Providing detailed error information
 */

import { PrismaClient } from '@prisma/client';
import { PatternCalculationService } from '../src/lib/pattern-analysis/pattern-calculator';

const prisma = new PrismaClient();

interface DebugResult {
  step: string;
  success: boolean;
  message: string;
  data?: any;
  error?: string;
}

class PatternCalculationDebugger {
  private results: DebugResult[] = [];

  async runDebug(): Promise<void> {
    console.log('🔍 Starting Pattern Calculation Debug...\n');

    try {
      await this.checkDatabaseConnection();
      await this.checkDataAvailability();
      await this.testPatternCalculation();
      await this.checkPatternResults();
    } catch (error) {
      this.addResult('Debug Suite', false, 'Debug suite failed', undefined, error instanceof Error ? error.message : String(error));
    } finally {
      await prisma.$disconnect();
      this.printResults();
    }
  }

  private async checkDatabaseConnection(): Promise<void> {
    console.log('🔌 Checking database connection...');
    
    try {
      await prisma.$connect();
      this.addResult('Database Connection', true, 'Successfully connected to database');
      console.log('✅ Database connected\n');
    } catch (error) {
      this.addResult('Database Connection', false, 'Failed to connect to database', undefined, error instanceof Error ? error.message : String(error));
      throw error;
    }
  }

  private async checkDataAvailability(): Promise<void> {
    console.log('📊 Checking data availability...');
    
    try {
      const [customerCount, materialCount, transactionCount] = await Promise.all([
        prisma.customer.count(),
        prisma.material.count(),
        prisma.transaction.count()
      ]);

      const dataStats = {
        customers: customerCount,
        materials: materialCount,
        transactions: transactionCount
      };

      this.addResult('Data Availability', true, 'Data availability checked', dataStats);
      
      console.log(`📈 Data Statistics:`);
      console.log(`   Customers: ${customerCount}`);
      console.log(`   Materials: ${materialCount}`);
      console.log(`   Transactions: ${transactionCount}\n`);

      if (transactionCount === 0) {
        console.log('⚠️  Warning: No transactions found. Pattern calculation may not work properly.');
      }
    } catch (error) {
      this.addResult('Data Availability', false, 'Failed to check data availability', undefined, error instanceof Error ? error.message : String(error));
      throw error;
    }
  }

  private async testPatternCalculation(): Promise<void> {
    console.log('⚙️  Testing pattern calculation...');
    
    try {
      const calculationService = new PatternCalculationService();
      
      // Test customer patterns
      console.log('   Testing customer patterns...');
      const customerResult = await calculationService.calculateCustomerPatterns();
      this.addResult('Customer Patterns', customerResult.success, customerResult.message, {
        recordsProcessed: customerResult.recordsProcessed,
        recordsUpdated: customerResult.recordsUpdated
      });

      // Test material patterns
      console.log('   Testing material patterns...');
      const materialResult = await calculationService.calculateMaterialPatterns();
      this.addResult('Material Patterns', materialResult.success, materialResult.message, {
        recordsProcessed: materialResult.recordsProcessed,
        recordsUpdated: materialResult.recordsUpdated
      });

      // Test revenue patterns
      console.log('   Testing revenue patterns...');
      const revenueResult = await calculationService.calculateRevenuePatterns();
      this.addResult('Revenue Patterns', revenueResult.success, revenueResult.message, {
        recordsProcessed: revenueResult.recordsProcessed,
        recordsUpdated: revenueResult.recordsUpdated
      });

      console.log('✅ Pattern calculation tests completed\n');
    } catch (error) {
      this.addResult('Pattern Calculation', false, 'Pattern calculation failed', undefined, error instanceof Error ? error.message : String(error));
      throw error;
    }
  }

  private async checkPatternResults(): Promise<void> {
    console.log('📋 Checking pattern calculation results...');
    
    try {
      const [customerPatterns, materialPatterns, revenuePatterns] = await Promise.all([
        prisma.customerPattern.count(),
        prisma.materialUsagePattern.count(),
        prisma.revenueTrend.count()
      ]);

      const patternStats = {
        customerPatterns,
        materialPatterns,
        revenuePatterns
      };

      this.addResult('Pattern Results', true, 'Pattern results checked', patternStats);
      
      console.log(`📊 Pattern Statistics:`);
      console.log(`   Customer Patterns: ${customerPatterns}`);
      console.log(`   Material Patterns: ${materialPatterns}`);
      console.log(`   Revenue Patterns: ${revenuePatterns}\n`);

      // Check recent patterns
      if (customerPatterns > 0) {
        const recentCustomerPattern = await prisma.customerPattern.findFirst({
          orderBy: { calculated_at: 'desc' },
          include: { customer: { select: { name: true } } }
        });
        
        if (recentCustomerPattern) {
          console.log(`🔍 Most recent customer pattern:`);
          console.log(`   Customer: ${recentCustomerPattern.customer.name}`);
          console.log(`   Calculated: ${recentCustomerPattern.calculated_at}`);
          console.log(`   Confidence: ${recentCustomerPattern.confidence_score}\n`);
        }
      }

      if (materialPatterns > 0) {
        const recentMaterialPattern = await prisma.materialUsagePattern.findFirst({
          orderBy: { calculated_at: 'desc' },
          include: { material: { select: { material_name: true } } }
        });
        
        if (recentMaterialPattern) {
          console.log(`🔍 Most recent material pattern:`);
          console.log(`   Material: ${recentMaterialPattern.material.material_name}`);
          console.log(`   Calculated: ${recentMaterialPattern.calculated_at}`);
          console.log(`   Confidence: ${recentMaterialPattern.confidence_score}\n`);
        }
      }

    } catch (error) {
      this.addResult('Pattern Results', false, 'Failed to check pattern results', undefined, error instanceof Error ? error.message : String(error));
      throw error;
    }
  }

  private addResult(step: string, success: boolean, message: string, data?: any, error?: string): void {
    this.results.push({ step, success, message, data, error });
  }

  private printResults(): void {
    console.log('📋 Debug Results Summary:');
    console.log('=========================\n');

    let passedSteps = 0;
    let totalSteps = this.results.length;

    this.results.forEach((result, index) => {
      const status = result.success ? '✅ PASS' : '❌ FAIL';
      console.log(`${index + 1}. ${status}: ${result.step} - ${result.message}`);
      
      if (result.data) {
        console.log(`   Data: ${JSON.stringify(result.data, null, 2)}`);
      }
      
      if (result.error) {
        console.log(`   Error: ${result.error}`);
      }
      
      console.log('');
      
      if (result.success) passedSteps++;
    });

    console.log(`📊 Summary: ${passedSteps}/${totalSteps} steps passed`);
    
    if (passedSteps === totalSteps) {
      console.log('🎉 All debug steps passed! Pattern calculation is working correctly.');
    } else {
      console.log('⚠️  Some debug steps failed. Please check the errors above.');
    }
  }
}

// Run the debug
async function main() {
  const debugger = new PatternCalculationDebugger();
  await debugger.runDebug();
  process.exit(0);
}

if (require.main === module) {
  main().catch((error) => {
    console.error('❌ Debug runner failed:', error);
    process.exit(1);
  });
}
