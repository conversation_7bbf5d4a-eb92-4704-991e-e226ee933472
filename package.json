{"name": "laundrysense", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "dev:ws": "nodemon --exec tsx server.ts", "websocket": "node websocket-server.js", "dev:full": "concurrently \"npm run dev\" \"npm run websocket\"", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:seed": "tsx prisma/seed.ts", "db:reset": "prisma migrate reset --force", "patterns:calculate": "tsx scripts/calculate-patterns.ts", "patterns:customers": "tsx scripts/calculate-patterns.ts customers", "patterns:materials": "tsx scripts/calculate-patterns.ts materials", "patterns:revenue": "tsx scripts/calculate-patterns.ts revenue", "context:test": "tsx scripts/test-context-detector.ts", "insights:test": "tsx scripts/test-insight-generator.ts", "websocket:test": "tsx scripts/test-websocket-connection.ts", "debug:patterns": "tsx scripts/debug-pattern-calculation.ts", "startup:check": "tsx scripts/startup-check.ts"}, "dependencies": {"@prisma/client": "^6.9.0", "lucide-react": "^0.513.0", "next": "15.3.3", "node-cron": "^3.0.3", "react": "^19.0.0", "react-dom": "^19.0.0", "ws": "^8.18.2", "zod": "^3.22.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/jest": "^29.5.8", "@types/node": "^20", "@types/node-cron": "^3.0.11", "@types/react": "^19", "@types/react-dom": "^19", "@types/ws": "^8.18.1", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.3", "jest": "^29.7.0", "jest-environment-node": "^29.7.0", "nodemon": "^3.1.10", "prisma": "^6.9.0", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "tsx": "^4.6.2", "typescript": "^5"}, "prisma": {"seed": "tsx prisma/seed.ts"}}