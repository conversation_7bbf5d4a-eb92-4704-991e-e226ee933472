# 🚀 LaundrySense Startup Guide

## ⚡ **CARA TERCEPAT MENJALANKAN SERVER**

### **1. Setup Database (Sekali saja)**
```bash
npm run db:generate
npm run db:push
npm run db:seed
```

### **2. Pilih Mode Server**

#### **🌟 RECOMMENDED: Integrated Server (Default)**
```bash
npm run dev
```
- ✅ Next.js + WebSocket dalam satu server (port 3000)
- ✅ Background pattern calculation jobs
- ✅ Auto-restart dengan nodemon
- ✅ Tidak ada konflik port
- ✅ Satu terminal saja

#### **🔧 Alternative: Separate Servers**
```bash
# Terminal 1: WebSocket Server
npm run websocket

# Terminal 2: Next.js Server
npm run dev:next
```
- ✅ Next.js di port 3000
- ✅ WebSocket di port 3001
- ✅ Debugging lebih mudah
- ✅ Independent scaling

#### **🚀 Automated Startup**
```bash
# Windows
start-laundrysense.bat

# macOS/Linux
./start-laundrysense.sh
```
- ✅ Automatic startup checks
- ✅ Pattern calculation
- ✅ Service monitoring

---

## 🎯 **Akses Dashboard**

Setelah server berjalan:
- **Web Dashboard**: http://localhost:3000/dashboard
- **WebSocket**: ws://localhost:3000 (integrated) atau ws://localhost:3001 (separate)

---

## ❌ **JANGAN LAKUKAN INI**

```bash
# ❌ SALAH - Akan konflik port!
npm run dev
npm run dev:ws  # Error: EADDRINUSE :::3000
```

---

## 🔍 **Cek Status Server**

```bash
# Cek port yang digunakan
netstat -ano | findstr :3000
netstat -ano | findstr :3001

# Kill process jika perlu
taskkill /PID <PID_NUMBER> /F
```

---

## 📋 **Troubleshooting Cepat**

| Masalah | Solusi |
|---------|--------|
| Prisma error | Jalankan `./fix-prisma.bat` |
| Port conflict | Gunakan hanya satu mode server |
| WebSocket tidak connect | Restart server, cek port |
| Database error | Cek MySQL service, cek .env |

---

**Happy Coding! 🎉**
